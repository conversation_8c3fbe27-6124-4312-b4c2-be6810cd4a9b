"""
测试 product_traffic_metrics_scheduler 中 execute_task 函数的简单执行方法
"""
import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from omni.mongo.mongo_client import init_models
from scheduler.traffic.product_traffic_metrics_scheduler import execute_task


async def simple_test_execute_task():
    """
    简单测试 execute_task 函数的执行
    """
    print("=" * 50)
    print("开始测试产品流量统计 execute_task 函数")
    print("=" * 50)
    
    try:
        # 步骤1: 初始化数据库模型
        print("步骤1: 初始化数据库模型...")
        await init_models()
        print("✓ 数据库模型初始化完成")
        
        # 步骤2: 执行任务
        print("\n步骤2: 执行产品流量统计任务...")
        await execute_task()
        print("✓ 产品流量统计任务执行完成")
        
        print("\n" + "=" * 50)
        print("测试执行成功！")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        raise


async def main():
    """
    主函数 - 运行测试
    """
    await simple_test_execute_task()


if __name__ == "__main__":
    print("启动产品流量统计任务测试...")
    try:
        # 运行异步测试
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试失败: {e}")
        sys.exit(1)
